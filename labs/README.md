# Labs Index

This directory contains hands-on labs for learning Kong Event Gateway:

## Lab 00: Configure Control Plane
[Lab 00: Configure Control Plane](00-configure-control-plane.md)
Set up Kong Event Gateway environment in Konnect with control plane and data plane components.

## Lab 01: Network Isolation Passthrough
[Lab 01: Network Isolation Passthrough](01-network-isolation-passthrough.md)
Learn basic proxy functionality with anonymous authentication and direct Kafka operations pass-through.

## Lab 02: Tenant Isolation
[Lab 02: Tenant Isolation](02-tenant-isolation.md)
Configure topic name filtering and prefix-based access control for multiple teams.

## Lab 03: Authentication Mediation
[Lab 03: Authentication Mediation](03-authentication.md)
Implement JWT-based authentication and anonymous access configurations for Kafka clusters.

## Lab 04: Message-Level Encryption
[Lab 04: Message-Level Encryption](04-policies-encryption.md)
Configure automatic message encryption and decryption using symmetric key encryption policies.

Each lab builds upon the previous one, so complete them in sequence for the best learning experience.
