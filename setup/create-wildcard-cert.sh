#!/bin/bash
set -e

# Create directory for certificates
mkdir -p config/certs
cd config/certs

# Generate CA private key
openssl genrsa -out ca.key 4096

# Create CA certificate
cat > ca.cnf << EOF
[req]
distinguished_name = req_distinguished_name
prompt = no
x509_extensions = v3_ca

[req_distinguished_name]
C = US
ST = California
L = San Francisco
O = Kong Inc
OU = Event Gateway Workshop
CN = Certificate Authority

[v3_ca]
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer:always
basicConstraints = critical, CA:true
keyUsage = critical, digitalSignature, cRLSign, keyCertSign
EOF

# Create self-signed CA certificate
openssl req -new -x509 -key ca.key -out ca.crt -days 3650 -config ca.cnf

# Generate wildcard certificate private key
openssl genrsa -out tls.key 2048

# Create config file for the wildcard certificate
cat > tls.cnf << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = California
L = San Francisco
O = Kong Inc
OU = Event Gateway Workshop
CN = *.127-0-0-1.sslip.io

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = 127-0-0-1.sslip.io
DNS.2 = *.127-0-0-1.sslip.io
DNS.3 = team-a.127-0-0-1.sslip.io
DNS.4 = *.team-a.127-0-0-1.sslip.io
DNS.5 = team-b.127-0-0-1.sslip.io
DNS.6 = *.team-b.127-0-0-1.sslip.io
DNS.7 = team-c.127-0-0-1.sslip.io
DNS.8 = *.team-c.127-0-0-1.sslip.io
DNS.9 = team-d.127-0-0-1.sslip.io
DNS.10 = *.team-d.127-0-0-1.sslip.io
DNS.11 = team-e.127-0-0-1.sslip.io
DNS.12 = *.team-e.127-0-0-1.sslip.io
DNS.13 = team-f.127-0-0-1.sslip.io
DNS.14 = *.team-f.127-0-0-1.sslip.io
DNS.15 = team-g.127-0-0-1.sslip.io
DNS.16 = *.team-g.127-0-0-1.sslip.io
DNS.17 = team-h.127-0-0-1.sslip.io
DNS.18 = *.team-h.127-0-0-1.sslip.io
EOF

# Create CSR (Certificate Signing Request)
openssl req -new -key tls.key -out tls.csr -config tls.cnf

# Create extension file for the certificate
cat > tls.ext << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = 127-0-0-1.sslip.io
DNS.2 = *.127-0-0-1.sslip.io
DNS.3 = team-a.127-0-0-1.sslip.io
DNS.4 = *.team-a.127-0-0-1.sslip.io
DNS.5 = team-b.127-0-0-1.sslip.io
DNS.6 = *.team-b.127-0-0-1.sslip.io
DNS.7 = team-c.127-0-0-1.sslip.io
DNS.8 = *.team-c.127-0-0-1.sslip.io
DNS.9 = team-d.127-0-0-1.sslip.io
DNS.10 = *.team-d.127-0-0-1.sslip.io
DNS.11 = team-e.127-0-0-1.sslip.io
DNS.12 = *.team-e.127-0-0-1.sslip.io
DNS.13 = team-f.127-0-0-1.sslip.io
DNS.14 = *.team-f.127-0-0-1.sslip.io
DNS.15 = team-g.127-0-0-1.sslip.io
DNS.16 = *.team-g.127-0-0-1.sslip.io
DNS.17 = team-h.127-0-0-1.sslip.io
DNS.18 = *.team-h.127-0-0-1.sslip.io
EOF

# Sign the wildcard certificate with our CA
openssl x509 -req -in tls.csr -CA ca.crt -CAkey ca.key \
    -CAcreateserial -out tls.crt -days 365 \
    -extfile tls.ext

# Create combined PEM file (some applications prefer this format)
cat tls.key tls.crt > tls.pem

# Create full chain PEM file (includes CA certificate)
cat tls.crt ca.crt > tls-chain.pem

echo "Certificates created successfully!"
echo "Multi-domain wildcard certificate supports:"
echo "  Base domains:"
echo "    - 127-0-0-1.sslip.io"
echo "    - *.127-0-0-1.sslip.io (any.127-0-0-1.sslip.io)"
echo "  Team-specific domains and their subdomains:"
echo "    - team-a.127-0-0-1.sslip.io + *.team-a.127-0-0-1.sslip.io (api.team-a.127-0-0-1.sslip.io)"
echo "    - team-b.127-0-0-1.sslip.io + *.team-b.127-0-0-1.sslip.io (gateway.team-b.127-0-0-1.sslip.io)"
echo "    - team-c.127-0-0-1.sslip.io + *.team-c.127-0-0-1.sslip.io (admin.team-c.127-0-0-1.sslip.io)"
echo "    - team-d.127-0-0-1.sslip.io + *.team-d.127-0-0-1.sslip.io"
echo "    - team-e.127-0-0-1.sslip.io + *.team-e.127-0-0-1.sslip.io"
echo "    - team-f.127-0-0-1.sslip.io + *.team-f.127-0-0-1.sslip.io"
echo "    - team-g.127-0-0-1.sslip.io + *.team-g.127-0-0-1.sslip.io"
echo "    - team-h.127-0-0-1.sslip.io + *.team-h.127-0-0-1.sslip.io"
echo ""
echo "Files generated:"
echo "  - ca.key (CA private key)"
echo "  - ca.crt (CA certificate)"
echo "  - tls.key (wildcard private key)"
echo "  - tls.crt (wildcard certificate)"
echo "  - tls.pem (combined key and certificate)"
echo "  - tls-chain.pem (certificate chain with CA)"
