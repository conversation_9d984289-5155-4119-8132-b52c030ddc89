#!/bin/bash

# Docker build script for Okta Kafka OIDC App
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="okta-kafka-app"
VERSION=${1:-"latest"}
FULL_IMAGE_NAME="${IMAGE_NAME}:${VERSION}"

echo -e "${BLUE}🐳 Building Docker image: ${FULL_IMAGE_NAME}${NC}"

# Build the Docker image
docker build -t "${FULL_IMAGE_NAME}" .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image built successfully: ${FULL_IMAGE_NAME}${NC}"
    
    # Show image info
    echo -e "${BLUE}📊 Image information:${NC}"
    docker images "${IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    echo -e "${YELLOW}💡 Usage examples:${NC}"
    echo -e "  Run with Docker:"
    echo -e "    ${GREEN}docker run -p 3000:3000 --env-file .env ${FULL_IMAGE_NAME}${NC}"
    echo -e ""
    echo -e "  Run with Docker Compose:"
    echo -e "    ${GREEN}docker-compose --env-file .env.docker.local up${NC}"
    echo -e ""
    echo -e "  Tag for registry:"
    echo -e "    ${GREEN}docker tag ${FULL_IMAGE_NAME} your-registry.com/${FULL_IMAGE_NAME}${NC}"
    
else
    echo -e "${RED}❌ Docker build failed${NC}"
    exit 1
fi
