# Docker Environment Variables Template
# Copy this file to .env.docker.local and fill in your actual values
# Then use: docker-compose --env-file .env.docker.local up

# Okta OIDC Configuration (REQUIRED)
OKTA_CLIENT_ID=your_okta_client_id_here
OKTA_CLIENT_SECRET=your_okta_client_secret_here
OKTA_ISSUER=https://your-domain.okta.com

# Optional Configuration
CALLBACK_URL=http://localhost:3000/auth/callback
APP_BASE_URL=http://localhost:3000
SESSION_SECRET=your-secure-session-secret-for-production
PORT=3000

# Kafka Configuration
KAFKA_BOOTSTRAP=localhost:19092

# For production, you might want to use different values:
# CALLBACK_URL=https://your-domain.com/auth/callback
# APP_BASE_URL=https://your-domain.com
# KAFKA_BOOTSTRAP=your-kafka-cluster:9092
