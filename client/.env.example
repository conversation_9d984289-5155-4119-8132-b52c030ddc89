# Okta OIDC Configuration
OKTA_CLIENT_ID=your_okta_client_id_here
OKTA_CLIENT_SECRET=your_okta_client_secret_here
OKTA_ISSUER=https://your-domain.okta.com

# Optional Configuration
CALLBACK_URL=http://localhost:3000/auth/callback
SESSION_SECRET=your-session-secret-change-in-production
PORT=3000

# Existing Kafka Configuration (if needed)
KAFKA_BOOTSTRAP=your_kafka_bootstrap_servers
KAFKA_TOKEN=your_kafka_token
