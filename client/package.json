{"name": "okta-oidc-client", "version": "1.0.0", "description": "Node.js application with Okta OIDC authentication", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "list-topics": "node listTopics.js", "docker:build": "./docker-build.sh", "docker:run": "docker run -p 3000:3000 --env-file .env okta-kafka-app:latest", "docker:compose:up": "docker-compose --env-file .env.docker.local up", "docker:compose:down": "docker-compose down"}, "dependencies": {"@okta/oidc-middleware": "^5.5.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.18.2", "kafkajs": "^2.2.4"}}