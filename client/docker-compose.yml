version: '3.8'

services:
  okta-kafka-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      # Okta OIDC Configuration
      - OKTA_CLIENT_ID=${OKTA_CLIENT_ID}
      - OKTA_CLIENT_SECRET=${OKTA_CLIENT_SECRET}
      - OKTA_ISSUER=${OKTA_ISSUER}
      
      # Optional Configuration
      - CALLBACK_URL=${CALLBACK_URL:-http://localhost:3000/auth/callback}
      - APP_BASE_URL=${APP_BASE_URL:-http://localhost:3000}
      - SESSION_SECRET=${SESSION_SECRET:-your-session-secret-change-in-production}
      - PORT=${PORT:-3000}
      
      # Kafka Configuration
      - KAFKA_BOOTSTRAP=${KAFKA_BOOTSTRAP:-localhost:19092}
    
    # Health check
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Restart policy
    restart: unless-stopped
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    
    # Logging
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Optional: Add Kafka for local development
  # Uncomment the following services if you want to run Kafka locally
  
  # zookeeper:
  #   image: confluentinc/cp-zookeeper:7.4.0
  #   environment:
  #     ZOOKEEPER_CLIENT_PORT: 2181
  #     ZOOKEEPER_TICK_TIME: 2000
  #   ports:
  #     - "2181:2181"
  
  # kafka:
  #   image: confluentinc/cp-kafka:7.4.0
  #   depends_on:
  #     - zookeeper
  #   ports:
  #     - "19092:19092"
  #   environment:
  #     KAFKA_BROKER_ID: 1
  #     KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
  #     KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:19092
  #     KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
  #     KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
  #     KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
