# Kong Event Gateway Workshop

This repository contains materials for the Kong SE team workshop focused on enabling teams with Kong Event Gateway.

## Prerequisites

- Docker Desktop v4.39 or higher (Windows/Mac)
- 8GB RAM available
- [jq](https://jqlang.github.io/jq/download/) installed
- Basic knowledge of Kong and Kong Konnect
- Basic knowledge of event-driven architecture
- Kafka CLI tool: [kafkactl](https://github.com/segmentio/kafkactl)

## Workshop Overview

- Introduction to Kong Event Gateway
- Key features and capabilities
- Deployment patterns
- Integration with existing Kong infrastructure
- Hands-on exercises and examples

## Getting Started

1. Clone this repository
2. Follow the setup instructions in the `/setup` directory
3. Complete the exercises in the `/labs` directory

## Additional Resources

- [Kong Event Gateway Documentation](https://developer.konghq.com/index/event-gateway/)
- [API Reference](https://developer.konghq.com/api/event-gateway/knep/v0/)
- [Examples](https://github.com/hguerrero/kong-event-gw-examples)
